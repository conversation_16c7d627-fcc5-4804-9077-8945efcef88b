import numpy as np
import os
os.environ['KERAS_BACKEND'] = 'jax'
import keras
from keras.preprocessing import image

# load the models when import "predictions.py"
model_elbow_frac = keras.models.load_model("weights/ResNet50_Elbow_frac.h5")
model_hand_frac = keras.models.load_model("weights/ResNet50_Hand_frac.h5")
model_shoulder_frac = keras.models.load_model("weights/ResNet50_Shoulder_frac.h5")
model_parts = keras.models.load_model("weights/ResNet50_BodyParts.h5")

# categories for each result by index

#   0-Elbow     1-Hand      2-Shoulder
categories_parts = ["Elbow", "Hand", "Shoulder"]

#   0-fractured     1-normal
categories_fracture = ['fractured', 'normal']


# get image and model name, the default model is "Parts"
# Parts - bone type predict model of 3 classes
# otherwise - fracture predict for each part
def predict(img, model="Parts", confidence_threshold=0.7):
    size = 224
    if model == 'Parts':
        chosen_model = model_parts
    else:
        if model == 'Elbow':
            chosen_model = model_elbow_frac
        elif model == 'Hand':
            chosen_model = model_hand_frac
        elif model == 'Shoulder':
            chosen_model = model_shoulder_frac

    # load image with 224px224p (the training model image size, rgb)
    temp_img = image.load_img(img, target_size=(size, size))
    x = image.img_to_array(temp_img)
    x = np.expand_dims(x, axis=0)
    images = np.vstack([x])

    # Get prediction probabilities
    predictions = chosen_model.predict(images)
    prediction_idx = np.argmax(predictions, axis=1)
    confidence = np.max(predictions, axis=1)[0]

    # chose the category and get the string prediction
    if model == 'Parts':
        # Check if confidence is above threshold for bone type detection
        if confidence < confidence_threshold:
            return "Invalid - Not a clear bone X-ray"
        prediction_str = categories_parts[prediction_idx.item()]
    else:
        # For fracture detection, also check confidence
        if confidence < confidence_threshold:
            return "Invalid - Unclear fracture detection"
        prediction_str = categories_fracture[prediction_idx.item()]

    return prediction_str

# Function to get prediction with confidence score
def predict_with_confidence(img, model="Parts"):
    size = 224
    if model == 'Parts':
        chosen_model = model_parts
    else:
        if model == 'Elbow':
            chosen_model = model_elbow_frac
        elif model == 'Hand':
            chosen_model = model_hand_frac
        elif model == 'Shoulder':
            chosen_model = model_shoulder_frac

    # load image with 224px224p (the training model image size, rgb)
    temp_img = image.load_img(img, target_size=(size, size))
    x = image.img_to_array(temp_img)
    x = np.expand_dims(x, axis=0)
    images = np.vstack([x])

    # Get prediction probabilities
    predictions = chosen_model.predict(images)
    prediction_idx = np.argmax(predictions, axis=1)
    confidence = np.max(predictions, axis=1)[0]

    # chose the category and get the string prediction
    if model == 'Parts':
        prediction_str = categories_parts[prediction_idx.item()]
    else:
        prediction_str = categories_fracture[prediction_idx.item()]

    return prediction_str, confidence
