import numpy as np
import os
os.environ['KERAS_BACKEND'] = 'jax'
import keras
from keras.preprocessing import image

# load the models when import "predictions.py"
model_elbow_frac = keras.models.load_model("weights/ResNet50_Elbow_frac.h5")
model_hand_frac = keras.models.load_model("weights/ResNet50_Hand_frac.h5")
model_shoulder_frac = keras.models.load_model("weights/ResNet50_Shoulder_frac.h5")
model_parts = keras.models.load_model("weights/ResNet50_BodyParts.h5")

# categories for each result by index

#   0-Elbow     1-Hand      2-Shoulder
categories_parts = ["Elbow", "Hand", "Shoulder"]

#   0-fractured     1-normal
categories_fracture = ['fractured', 'normal']


# Function to check if image looks like an X-ray
def is_xray_image(img_path):
    """
    Basic check to determine if an image looks like an X-ray
    X-rays typically have:
    - Grayscale or near-grayscale appearance
    - High contrast between bones (bright) and soft tissue (dark)
    - Limited color variation
    """
    try:
        from PIL import Image

        # Load image
        img = Image.open(img_path)
        img_array = np.array(img)

        # Convert to grayscale if needed
        if len(img_array.shape) == 3:
            # Check if image is already grayscale-like (low color variation)
            r, g, b = img_array[:,:,0], img_array[:,:,1], img_array[:,:,2]
            color_diff = np.mean(np.abs(r - g)) + np.mean(np.abs(g - b)) + np.mean(np.abs(r - b))

            # If color difference is high, it's likely a color photo
            if color_diff > 30:  # Threshold for color variation
                return False

            # Convert to grayscale for further analysis
            gray = np.mean(img_array, axis=2)
        else:
            gray = img_array

        # Check contrast (X-rays typically have high contrast)
        contrast = np.std(gray)
        if contrast < 15:  # Low contrast suggests not an X-ray
            return False

        # Check mean intensity (X-rays are typically darker overall)
        mean_intensity = np.mean(gray)
        if mean_intensity > 180:  # Too bright, likely a regular photo
            return False

        # Check if image has typical X-ray characteristics
        # X-rays usually have a good distribution of dark and bright areas
        hist, _ = np.histogram(gray, bins=256, range=(0, 255))

        # Check if there's reasonable distribution across intensity levels
        dark_pixels = np.sum(hist[:85])  # Dark areas (soft tissue)
        bright_pixels = np.sum(hist[170:])  # Bright areas (bones)
        mid_pixels = np.sum(hist[85:170])  # Mid-tone areas
        total_pixels = gray.size

        dark_ratio = dark_pixels / total_pixels
        bright_ratio = bright_pixels / total_pixels
        mid_ratio = mid_pixels / total_pixels

        # X-rays should have both dark and bright regions, but not too much mid-tone
        if dark_ratio < 0.15 or bright_ratio < 0.03:
            return False

        # Regular photos often have too much mid-tone content
        if mid_ratio > 0.7:
            return False

        return True

    except Exception as e:
        print(f"Error in X-ray validation: {e}")
        return False

# get image and model name, the default model is "Parts"
# Parts - bone type predict model of 3 classes
# otherwise - fracture predict for each part
def predict(img, model="Parts", confidence_threshold=0.90):
    # First check if the image looks like an X-ray
    if not is_xray_image(img):
        return "Invalid - Not an X-ray image"

    size = 224
    if model == 'Parts':
        chosen_model = model_parts
    else:
        if model == 'Elbow':
            chosen_model = model_elbow_frac
        elif model == 'Hand':
            chosen_model = model_hand_frac
        elif model == 'Shoulder':
            chosen_model = model_shoulder_frac

    # load image with 224px224p (the training model image size, rgb)
    temp_img = image.load_img(img, target_size=(size, size))
    x = image.img_to_array(temp_img)
    x = np.expand_dims(x, axis=0)
    images = np.vstack([x])

    # Get prediction probabilities
    predictions = chosen_model.predict(images)
    prediction_idx = np.argmax(predictions, axis=1)
    confidence = np.max(predictions, axis=1)[0]

    # chose the category and get the string prediction
    if model == 'Parts':
        # Check if confidence is above threshold for bone type detection
        if confidence < confidence_threshold:
            return "Invalid - Not a clear bone X-ray"
        prediction_str = categories_parts[prediction_idx.item()]
    else:
        # For fracture detection, also check confidence
        if confidence < confidence_threshold:
            return "Invalid - Unclear fracture detection"
        prediction_str = categories_fracture[prediction_idx.item()]

    return prediction_str

# Function to get prediction with confidence score
def predict_with_confidence(img, model="Parts"):
    size = 224
    if model == 'Parts':
        chosen_model = model_parts
    else:
        if model == 'Elbow':
            chosen_model = model_elbow_frac
        elif model == 'Hand':
            chosen_model = model_hand_frac
        elif model == 'Shoulder':
            chosen_model = model_shoulder_frac

    # load image with 224px224p (the training model image size, rgb)
    temp_img = image.load_img(img, target_size=(size, size))
    x = image.img_to_array(temp_img)
    x = np.expand_dims(x, axis=0)
    images = np.vstack([x])

    # Get prediction probabilities
    predictions = chosen_model.predict(images)
    prediction_idx = np.argmax(predictions, axis=1)
    confidence = np.max(predictions, axis=1)[0]

    # chose the category and get the string prediction
    if model == 'Parts':
        prediction_str = categories_parts[prediction_idx.item()]
    else:
        prediction_str = categories_fracture[prediction_idx.item()]

    return prediction_str, confidence
