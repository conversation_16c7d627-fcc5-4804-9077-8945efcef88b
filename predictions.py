import numpy as np
import os
os.environ['KERAS_BACKEND'] = 'jax'
import keras
from keras.preprocessing import image

# load the models when import "predictions.py"
model_elbow_frac = keras.models.load_model("weights/ResNet50_Elbow_frac.h5")
model_hand_frac = keras.models.load_model("weights/ResNet50_Hand_frac.h5")
model_shoulder_frac = keras.models.load_model("weights/ResNet50_Shoulder_frac.h5")
model_parts = keras.models.load_model("weights/ResNet50_BodyParts.h5")

# categories for each result by index

#   0-Elbow     1-Hand      2-Shoulder
categories_parts = ["Elbow", "Hand", "Shoulder"]

#   0-fractured     1-normal
categories_fracture = ['fractured', 'normal']


# Function to check if image looks like an X-ray
def is_xray_image(img_path):
    """
    Simple check to determine if an image looks like an X-ray
    Focus on the most obvious differences between X-rays and regular photos
    """
    try:
        from PIL import Image

        # Load image
        img = Image.open(img_path)
        img_array = np.array(img)

        # Convert to grayscale if needed
        if len(img_array.shape) == 3:
            # Check if image is colorful (regular photos are usually colorful)
            r, g, b = img_array[:,:,0], img_array[:,:,1], img_array[:,:,2]
            color_diff = np.mean(np.abs(r - g)) + np.mean(np.abs(g - b)) + np.mean(np.abs(r - b))

            # If there's significant color variation, it's likely a regular photo
            if color_diff > 40:
                return False

            # Convert to grayscale for further analysis
            gray = np.mean(img_array, axis=2)
        else:
            gray = img_array

        # Check if image is too bright overall (regular photos are often brighter)
        mean_intensity = np.mean(gray)
        if mean_intensity > 200:
            return False

        # Check if image has very low contrast (some regular photos)
        contrast = np.std(gray)
        if contrast < 5:
            return False

        return True

    except Exception as e:
        print(f"Error in X-ray validation: {e}")
        return False

# Function to detect if X-ray is of other bone types (not elbow/hand/shoulder)
def is_other_bone_xray(img_path):
    """
    Detect if the X-ray is of other bone types like leg, spine, chest, etc.
    This uses additional heuristics beyond the basic X-ray detection
    """
    try:
        from PIL import Image

        # Load image
        img = Image.open(img_path)
        img_array = np.array(img)

        # Convert to grayscale if needed
        if len(img_array.shape) == 3:
            gray = np.mean(img_array, axis=2)
        else:
            gray = img_array

        # Check image dimensions and aspect ratio
        height, width = gray.shape
        aspect_ratio = width / height

        # Very long/tall images might be spine X-rays
        if aspect_ratio < 0.3 or aspect_ratio > 3.0:
            return True

        # Check for very large bright regions (might be chest X-rays)
        bright_threshold = 200
        bright_pixels = np.sum(gray > bright_threshold)
        bright_ratio = bright_pixels / gray.size

        # Chest X-rays often have large bright lung areas
        if bright_ratio > 0.4:
            return True

        # Check for very uniform dark regions (might be leg X-rays)
        dark_threshold = 50
        dark_pixels = np.sum(gray < dark_threshold)
        dark_ratio = dark_pixels / gray.size

        # Some bone X-rays have very large dark areas
        if dark_ratio > 0.8:
            return True

        return False

    except Exception as e:
        print(f"Error in other bone detection: {e}")
        return False

# get image and model name, the default model is "Parts"
# Parts - bone type predict model of 3 classes
# otherwise - fracture predict for each part
def predict(img, model="Parts", confidence_threshold=0.70):
    # First do a basic check if the image looks like an X-ray
    basic_xray_check = is_xray_image(img)

    # If it clearly fails basic X-ray checks, it's likely not an X-ray
    if not basic_xray_check:
        return "Invalid - Not an X-ray image"

    size = 224
    if model == 'Parts':
        chosen_model = model_parts
    else:
        if model == 'Elbow':
            chosen_model = model_elbow_frac
        elif model == 'Hand':
            chosen_model = model_hand_frac
        elif model == 'Shoulder':
            chosen_model = model_shoulder_frac

    # load image with 224px224p (the training model image size, rgb)
    temp_img = image.load_img(img, target_size=(size, size))
    x = image.img_to_array(temp_img)
    x = np.expand_dims(x, axis=0)
    images = np.vstack([x])

    # Get prediction probabilities
    predictions = chosen_model.predict(images)
    prediction_idx = np.argmax(predictions, axis=1)
    confidence = np.max(predictions, axis=1)[0]

    # Debug output
    print(f"Model: {model}, Confidence: {confidence:.3f}, Prediction idx: {prediction_idx[0]}")

    # chose the category and get the string prediction
    if model == 'Parts':
        # Check if this is clearly not an X-ray based on very low confidence
        if confidence < 0.3:
            return "Invalid - Not an X-ray image"

        # Check if confidence is too low for bone type detection
        if confidence < confidence_threshold:
            # Could be other bone types or unclear X-ray
            if not basic_xray_check:
                return "Invalid - Not an X-ray image"
            else:
                return "Invalid - Only elbow, hand, shoulder accepted"

        prediction_str = categories_parts[prediction_idx.item()]

        # Additional validation: if confidence is borderline and other checks suggest other bone
        if confidence < 0.75 and is_other_bone_xray(img):
            return "Invalid - Only elbow, hand, shoulder accepted"

    else:
        # For fracture detection, also check confidence
        if confidence < confidence_threshold:
            return "Invalid - Unclear fracture detection"
        prediction_str = categories_fracture[prediction_idx.item()]

    return prediction_str

# Function to get prediction with confidence score
def predict_with_confidence(img, model="Parts"):
    size = 224
    if model == 'Parts':
        chosen_model = model_parts
    else:
        if model == 'Elbow':
            chosen_model = model_elbow_frac
        elif model == 'Hand':
            chosen_model = model_hand_frac
        elif model == 'Shoulder':
            chosen_model = model_shoulder_frac

    # load image with 224px224p (the training model image size, rgb)
    temp_img = image.load_img(img, target_size=(size, size))
    x = image.img_to_array(temp_img)
    x = np.expand_dims(x, axis=0)
    images = np.vstack([x])

    # Get prediction probabilities
    predictions = chosen_model.predict(images)
    prediction_idx = np.argmax(predictions, axis=1)
    confidence = np.max(predictions, axis=1)[0]

    # chose the category and get the string prediction
    if model == 'Parts':
        prediction_str = categories_parts[prediction_idx.item()]
    else:
        prediction_str = categories_fracture[prediction_idx.item()]

    return prediction_str, confidence
